# Basin Real Estate WordPress Theme

## Description
A modern, responsive WordPress theme designed specifically for the Permian Basin Real Estate Guide. This theme features custom post types for real estate listings and a hierarchical directory taxonomy for organizing properties.

## Features

### Custom Post Types
- **Listing**: Custom post type for real estate properties with key 'listing'
- Supports: title, editor, thumbnail, excerpt, custom fields, revisions
- Has archive page and is publicly queryable
- SEO-friendly URLs with 'listings' slug

### Custom Taxonomies
- **Directory**: Hierarchical taxonomy with key 'directory'
- Allows categorization of listings into organized directories
- Supports parent-child relationships for better organization

### Custom Fields
Each listing includes the following custom fields:
- Price
- Address
- Number of Bedrooms
- Number of Bathrooms
- Square Footage

### Responsive Design
- Mobile-first approach
- Optimized for tablets and desktop
- Modern CSS Grid and Flexbox layouts
- Touch-friendly navigation

### Theme Features
- Custom logo support
- Navigation menus (Primary and Footer)
- Widget areas (Sidebar and Footer)
- Post thumbnails
- HTML5 markup
- Translation ready

## File Structure

### Core Template Files
- `style.css` - Main stylesheet with theme information and responsive styles
- `functions.php` - Theme functions, custom post types, and taxonomies
- `index.php` - Main template file for posts and listings
- `page.php` - Template for static pages
- `single-listing.php` - Template for individual listing pages
- `sidebar.php` - Sidebar template with default widgets
- `searchform.php` - Custom search form template

### Key Functions

#### Custom Post Type Registration
```php
register_post_type('listing', $args);
```

#### Custom Taxonomy Registration
```php
register_taxonomy('directory', array('listing'), $args);
```

#### Custom Meta Fields
- Listing price, address, bedrooms, bathrooms, square feet
- Admin interface for easy editing
- Secure saving with nonce verification

## Installation

1. Upload the `tester1` folder to `/wp-content/themes/`
2. Activate the theme through the WordPress admin
3. Go to Appearance > Menus to set up navigation
4. Visit Listings > Add New to create your first property listing
5. Use Listings > Directories to organize your properties

## Customization

### Colors
The theme uses a professional color scheme:
- Primary: #3498db (Blue)
- Secondary: #e74c3c (Red)
- Dark: #2c3e50
- Light: #f8f9fa

### Typography
- Font Family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- Responsive font sizes
- Optimized line heights for readability

### Layout
- Container max-width: 1200px
- Grid-based listing display
- Flexible sidebar layout

## Browser Support
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Version History
- 1.0 - Initial release with core functionality

## Support
For theme support and customization, contact the Basin Real Estate development team.

## License
This theme is licensed under GPL v2 or later.
