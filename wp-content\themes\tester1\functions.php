<?php
/**
 * Basin Real Estate Theme Functions
 * 
 * @package Basin_Real_Estate
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function basin_real_estate_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'basin-real-estate'),
        'footer' => __('Footer Menu', 'basin-real-estate'),
    ));
}
add_action('after_setup_theme', 'basin_real_estate_setup');

/**
 * Enqueue scripts and styles
 */
function basin_real_estate_scripts() {
    wp_enqueue_style('basin-real-estate-style', get_stylesheet_uri(), array(), '1.0');
    wp_enqueue_script('basin-real-estate-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0', true);
}
add_action('wp_enqueue_scripts', 'basin_real_estate_scripts');

/**
 * Register Custom Post Type: Listing
 */
function register_listing_post_type() {
    $labels = array(
        'name'                  => _x('Listings', 'Post Type General Name', 'basin-real-estate'),
        'singular_name'         => _x('Listing', 'Post Type Singular Name', 'basin-real-estate'),
        'menu_name'             => __('Listings', 'basin-real-estate'),
        'name_admin_bar'        => __('Listing', 'basin-real-estate'),
        'archives'              => __('Listing Archives', 'basin-real-estate'),
        'attributes'            => __('Listing Attributes', 'basin-real-estate'),
        'parent_item_colon'     => __('Parent Listing:', 'basin-real-estate'),
        'all_items'             => __('All Listings', 'basin-real-estate'),
        'add_new_item'          => __('Add New Listing', 'basin-real-estate'),
        'add_new'               => __('Add New', 'basin-real-estate'),
        'new_item'              => __('New Listing', 'basin-real-estate'),
        'edit_item'             => __('Edit Listing', 'basin-real-estate'),
        'update_item'           => __('Update Listing', 'basin-real-estate'),
        'view_item'             => __('View Listing', 'basin-real-estate'),
        'view_items'            => __('View Listings', 'basin-real-estate'),
        'search_items'          => __('Search Listing', 'basin-real-estate'),
        'not_found'             => __('Not found', 'basin-real-estate'),
        'not_found_in_trash'    => __('Not found in Trash', 'basin-real-estate'),
        'featured_image'        => __('Featured Image', 'basin-real-estate'),
        'set_featured_image'    => __('Set featured image', 'basin-real-estate'),
        'remove_featured_image' => __('Remove featured image', 'basin-real-estate'),
        'use_featured_image'    => __('Use as featured image', 'basin-real-estate'),
        'insert_into_item'      => __('Insert into listing', 'basin-real-estate'),
        'uploaded_to_this_item' => __('Uploaded to this listing', 'basin-real-estate'),
        'items_list'            => __('Listings list', 'basin-real-estate'),
        'items_list_navigation' => __('Listings list navigation', 'basin-real-estate'),
        'filter_items_list'     => __('Filter listings list', 'basin-real-estate'),
    );

    $args = array(
        'label'                 => __('Listing', 'basin-real-estate'),
        'description'           => __('Real Estate Listings for Permian Basin', 'basin-real-estate'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields', 'revisions'),
        'taxonomies'            => array('directory'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-building',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'listings'),
    );

    register_post_type('listing', $args);
}
add_action('init', 'register_listing_post_type', 0);

/**
 * Register Custom Taxonomy: Directory (Hierarchical)
 */
function register_directory_taxonomy() {
    $labels = array(
        'name'                       => _x('Directories', 'Taxonomy General Name', 'basin-real-estate'),
        'singular_name'              => _x('Directory', 'Taxonomy Singular Name', 'basin-real-estate'),
        'menu_name'                  => __('Directories', 'basin-real-estate'),
        'all_items'                  => __('All Directories', 'basin-real-estate'),
        'parent_item'                => __('Parent Directory', 'basin-real-estate'),
        'parent_item_colon'          => __('Parent Directory:', 'basin-real-estate'),
        'new_item_name'              => __('New Directory Name', 'basin-real-estate'),
        'add_new_item'               => __('Add New Directory', 'basin-real-estate'),
        'edit_item'                  => __('Edit Directory', 'basin-real-estate'),
        'update_item'                => __('Update Directory', 'basin-real-estate'),
        'view_item'                  => __('View Directory', 'basin-real-estate'),
        'separate_items_with_commas' => __('Separate directories with commas', 'basin-real-estate'),
        'add_or_remove_items'        => __('Add or remove directories', 'basin-real-estate'),
        'choose_from_most_used'      => __('Choose from the most used', 'basin-real-estate'),
        'popular_items'              => __('Popular Directories', 'basin-real-estate'),
        'search_items'               => __('Search Directories', 'basin-real-estate'),
        'not_found'                  => __('Not Found', 'basin-real-estate'),
        'no_terms'                   => __('No directories', 'basin-real-estate'),
        'items_list'                 => __('Directories list', 'basin-real-estate'),
        'items_list_navigation'      => __('Directories list navigation', 'basin-real-estate'),
    );

    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => true,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
        'show_in_rest'               => true,
        'rewrite'                    => array('slug' => 'directory'),
    );

    register_taxonomy('directory', array('listing'), $args);
}
add_action('init', 'register_directory_taxonomy', 0);

/**
 * Widget Areas
 */
function basin_real_estate_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'basin-real-estate'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'basin-real-estate'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => __('Footer', 'basin-real-estate'),
        'id'            => 'footer-1',
        'description'   => __('Add footer widgets here.', 'basin-real-estate'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'basin_real_estate_widgets_init');

/**
 * Custom excerpt length
 */
function basin_real_estate_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'basin_real_estate_excerpt_length', 999);

/**
 * Custom excerpt more
 */
function basin_real_estate_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'basin_real_estate_excerpt_more');

/**
 * Add custom fields support for listings
 */
function basin_real_estate_add_listing_meta_boxes() {
    add_meta_box(
        'listing-details',
        __('Listing Details', 'basin-real-estate'),
        'basin_real_estate_listing_details_callback',
        'listing',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'basin_real_estate_add_listing_meta_boxes');

/**
 * Listing details meta box callback
 */
function basin_real_estate_listing_details_callback($post) {
    wp_nonce_field('basin_real_estate_save_listing_details', 'basin_real_estate_listing_details_nonce');
    
    $price = get_post_meta($post->ID, '_listing_price', true);
    $address = get_post_meta($post->ID, '_listing_address', true);
    $bedrooms = get_post_meta($post->ID, '_listing_bedrooms', true);
    $bathrooms = get_post_meta($post->ID, '_listing_bathrooms', true);
    $square_feet = get_post_meta($post->ID, '_listing_square_feet', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="listing_price">' . __('Price', 'basin-real-estate') . '</label></th>';
    echo '<td><input type="text" id="listing_price" name="listing_price" value="' . esc_attr($price) . '" /></td></tr>';
    
    echo '<tr><th><label for="listing_address">' . __('Address', 'basin-real-estate') . '</label></th>';
    echo '<td><input type="text" id="listing_address" name="listing_address" value="' . esc_attr($address) . '" /></td></tr>';
    
    echo '<tr><th><label for="listing_bedrooms">' . __('Bedrooms', 'basin-real-estate') . '</label></th>';
    echo '<td><input type="number" id="listing_bedrooms" name="listing_bedrooms" value="' . esc_attr($bedrooms) . '" /></td></tr>';
    
    echo '<tr><th><label for="listing_bathrooms">' . __('Bathrooms', 'basin-real-estate') . '</label></th>';
    echo '<td><input type="number" id="listing_bathrooms" name="listing_bathrooms" value="' . esc_attr($bathrooms) . '" step="0.5" /></td></tr>';
    
    echo '<tr><th><label for="listing_square_feet">' . __('Square Feet', 'basin-real-estate') . '</label></th>';
    echo '<td><input type="number" id="listing_square_feet" name="listing_square_feet" value="' . esc_attr($square_feet) . '" /></td></tr>';
    echo '</table>';
}

/**
 * Save listing details
 */
function basin_real_estate_save_listing_details($post_id) {
    if (!isset($_POST['basin_real_estate_listing_details_nonce']) || 
        !wp_verify_nonce($_POST['basin_real_estate_listing_details_nonce'], 'basin_real_estate_save_listing_details')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    $fields = array('listing_price', 'listing_address', 'listing_bedrooms', 'listing_bathrooms', 'listing_square_feet');
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'basin_real_estate_save_listing_details');
