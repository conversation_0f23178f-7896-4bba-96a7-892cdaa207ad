<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="site-branding">
                <?php if (has_custom_logo()) : ?>
                    <div class="site-logo">
                        <?php the_custom_logo(); ?>
                    </div>
                <?php endif; ?>
                
                <h1 class="site-title">
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                        <?php bloginfo('name'); ?>
                    </a>
                </h1>
                
                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="site-description"><?php echo $description; ?></p>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <nav id="site-navigation" class="main-navigation">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'fallback_cb'    => 'basin_real_estate_fallback_menu',
            ));
            ?>
        </div>
    </nav>

    <div id="content" class="site-content">
        <div class="container">
            <div class="content-area">
                <main id="main" class="site-main">
                    
                    <?php if (is_home() && !is_front_page()) : ?>
                        <header class="page-header">
                            <h1 class="page-title"><?php single_post_title(); ?></h1>
                        </header>
                    <?php endif; ?>

                    <?php if (have_posts()) : ?>
                        
                        <?php if (is_post_type_archive('listing')) : ?>
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('Real Estate Listings', 'basin-real-estate'); ?></h1>
                                <div class="archive-description">
                                    <p><?php _e('Browse our comprehensive directory of Permian Basin real estate listings.', 'basin-real-estate'); ?></p>
                                </div>
                            </header>
                            
                            <!-- Directory Filter -->
                            <div class="directory-filter">
                                <h3><?php _e('Filter by Directory', 'basin-real-estate'); ?></h3>
                                <?php
                                $directories = get_terms(array(
                                    'taxonomy' => 'directory',
                                    'hide_empty' => false,
                                ));
                                
                                if (!empty($directories) && !is_wp_error($directories)) :
                                ?>
                                    <ul class="filter-list">
                                        <li><a href="<?php echo get_post_type_archive_link('listing'); ?>"><?php _e('All Listings', 'basin-real-estate'); ?></a></li>
                                        <?php foreach ($directories as $directory) : ?>
                                            <li>
                                                <a href="<?php echo get_term_link($directory); ?>">
                                                    <?php echo esc_html($directory->name); ?>
                                                    <span class="count">(<?php echo $directory->count; ?>)</span>
                                                </a>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                            </div>
                            
                            <div class="listing-grid">
                        <?php endif; ?>

                        <?php while (have_posts()) : the_post(); ?>
                            
                            <?php if (get_post_type() === 'listing') : ?>
                                <!-- Listing Card -->
                                <article id="post-<?php the_ID(); ?>" <?php post_class('listing-card'); ?>>
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="listing-image-container">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium', array('class' => 'listing-image')); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="listing-details">
                                        <h2 class="listing-title">
                                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                        </h2>
                                        
                                        <?php
                                        $price = get_post_meta(get_the_ID(), '_listing_price', true);
                                        $address = get_post_meta(get_the_ID(), '_listing_address', true);
                                        $bedrooms = get_post_meta(get_the_ID(), '_listing_bedrooms', true);
                                        $bathrooms = get_post_meta(get_the_ID(), '_listing_bathrooms', true);
                                        $square_feet = get_post_meta(get_the_ID(), '_listing_square_feet', true);
                                        ?>
                                        
                                        <?php if ($price) : ?>
                                            <div class="listing-price">$<?php echo number_format($price); ?></div>
                                        <?php endif; ?>
                                        
                                        <?php if ($address) : ?>
                                            <div class="listing-address"><?php echo esc_html($address); ?></div>
                                        <?php endif; ?>
                                        
                                        <div class="listing-features">
                                            <?php if ($bedrooms) : ?>
                                                <span class="bedrooms"><?php echo $bedrooms; ?> bed</span>
                                            <?php endif; ?>
                                            <?php if ($bathrooms) : ?>
                                                <span class="bathrooms"><?php echo $bathrooms; ?> bath</span>
                                            <?php endif; ?>
                                            <?php if ($square_feet) : ?>
                                                <span class="square-feet"><?php echo number_format($square_feet); ?> sq ft</span>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="listing-excerpt">
                                            <?php the_excerpt(); ?>
                                        </div>
                                        
                                        <div class="listing-directories">
                                            <?php
                                            $terms = get_the_terms(get_the_ID(), 'directory');
                                            if ($terms && !is_wp_error($terms)) :
                                                foreach ($terms as $term) :
                                            ?>
                                                <span class="directory-tag">
                                                    <a href="<?php echo get_term_link($term); ?>"><?php echo esc_html($term->name); ?></a>
                                                </span>
                                            <?php endforeach; endif; ?>
                                        </div>
                                        
                                        <a href="<?php the_permalink(); ?>" class="btn btn-primary"><?php _e('View Details', 'basin-real-estate'); ?></a>
                                    </div>
                                </article>
                                
                            <?php else : ?>
                                <!-- Regular Post -->
                                <article id="post-<?php the_ID(); ?>" <?php post_class('post'); ?>>
                                    <header class="post-header">
                                        <h2 class="post-title">
                                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                        </h2>
                                        <div class="post-meta">
                                            <span class="posted-on">
                                                <?php _e('Posted on', 'basin-real-estate'); ?>
                                                <a href="<?php the_permalink(); ?>">
                                                    <time datetime="<?php echo get_the_date('c'); ?>"><?php echo get_the_date(); ?></time>
                                                </a>
                                            </span>
                                            <span class="byline">
                                                <?php _e('by', 'basin-real-estate'); ?>
                                                <a href="<?php echo get_author_posts_url(get_the_author_meta('ID')); ?>"><?php the_author(); ?></a>
                                            </span>
                                        </div>
                                    </header>
                                    
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="post-thumbnail">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('medium'); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="post-content">
                                        <?php the_excerpt(); ?>
                                        <a href="<?php the_permalink(); ?>" class="btn"><?php _e('Read More', 'basin-real-estate'); ?></a>
                                    </div>
                                </article>
                            <?php endif; ?>

                        <?php endwhile; ?>
                        
                        <?php if (is_post_type_archive('listing')) : ?>
                            </div> <!-- .listing-grid -->
                        <?php endif; ?>

                        <?php
                        the_posts_navigation(array(
                            'prev_text' => __('&laquo; Older posts', 'basin-real-estate'),
                            'next_text' => __('Newer posts &raquo;', 'basin-real-estate'),
                        ));
                        ?>

                    <?php else : ?>
                        
                        <section class="no-results not-found">
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('Nothing here', 'basin-real-estate'); ?></h1>
                            </header>
                            
                            <div class="page-content">
                                <?php if (is_home() && current_user_can('publish_posts')) : ?>
                                    <p><?php printf(__('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'basin-real-estate'), esc_url(admin_url('post-new.php'))); ?></p>
                                <?php elseif (is_search()) : ?>
                                    <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'basin-real-estate'); ?></p>
                                    <?php get_search_form(); ?>
                                <?php else : ?>
                                    <p><?php _e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'basin-real-estate'); ?></p>
                                    <?php get_search_form(); ?>
                                <?php endif; ?>
                            </div>
                        </section>

                    <?php endif; ?>

                </main>

                <?php get_sidebar(); ?>
            </div>
        </div>
    </div>

    <footer id="colophon" class="site-footer">
        <div class="container">
            <?php if (is_active_sidebar('footer-1')) : ?>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-1'); ?>
                </div>
            <?php endif; ?>
            
            <div class="site-info">
                <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('Permian Basin Real Estate Guide', 'basin-real-estate'); ?></p>
            </div>
        </div>
    </footer>
</div>

<?php wp_footer(); ?>

</body>
</html>

<?php
/**
 * Fallback menu function
 */
function basin_real_estate_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . home_url('/') . '">' . __('Home', 'basin-real-estate') . '</a></li>';
    echo '<li><a href="' . get_post_type_archive_link('listing') . '">' . __('Listings', 'basin-real-estate') . '</a></li>';
    echo '</ul>';
}
?>
