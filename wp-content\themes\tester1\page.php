<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="site-branding">
                <?php if (has_custom_logo()) : ?>
                    <div class="site-logo">
                        <?php the_custom_logo(); ?>
                    </div>
                <?php endif; ?>
                
                <h1 class="site-title">
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                        <?php bloginfo('name'); ?>
                    </a>
                </h1>
                
                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="site-description"><?php echo $description; ?></p>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <nav id="site-navigation" class="main-navigation">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'fallback_cb'    => 'basin_real_estate_fallback_menu',
            ));
            ?>
        </div>
    </nav>

    <div id="content" class="site-content">
        <div class="container">
            <div class="content-area">
                <main id="main" class="site-main">

                    <?php while (have_posts()) : the_post(); ?>

                        <article id="post-<?php the_ID(); ?>" <?php post_class('page'); ?>>
                            <header class="page-header">
                                <h1 class="page-title"><?php the_title(); ?></h1>
                                
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="page-featured-image">
                                        <?php the_post_thumbnail('large', array('class' => 'responsive-image')); ?>
                                    </div>
                                <?php endif; ?>
                            </header>

                            <div class="page-content">
                                <?php
                                the_content();

                                wp_link_pages(array(
                                    'before' => '<div class="page-links">' . __('Pages:', 'basin-real-estate'),
                                    'after'  => '</div>',
                                ));
                                ?>
                            </div>

                            <?php if (get_edit_post_link()) : ?>
                                <footer class="entry-footer">
                                    <div class="edit-link">
                                        <?php edit_post_link(__('Edit', 'basin-real-estate'), '<span class="edit-link">', '</span>'); ?>
                                    </div>
                                </footer>
                            <?php endif; ?>
                        </article>

                        <?php
                        // If comments are open or we have at least one comment, load up the comment template.
                        if (comments_open() || get_comments_number()) :
                            comments_template();
                        endif;
                        ?>

                    <?php endwhile; ?>

                </main>

                <?php get_sidebar(); ?>
            </div>
        </div>
    </div>

    <footer id="colophon" class="site-footer">
        <div class="container">
            <?php if (is_active_sidebar('footer-1')) : ?>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-1'); ?>
                </div>
            <?php endif; ?>
            
            <div class="site-info">
                <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('Permian Basin Real Estate Guide', 'basin-real-estate'); ?></p>
            </div>
        </div>
    </footer>
</div>

<?php wp_footer(); ?>

</body>
</html>
