<?php
/**
 * The sidebar containing the main widget area
 *
 * @package Basin_Real_Estate
 */

if (!is_active_sidebar('sidebar-1')) {
    return;
}
?>

<aside id="secondary" class="widget-area sidebar">
    <?php dynamic_sidebar('sidebar-1'); ?>
    
    <?php if (!dynamic_sidebar('sidebar-1')) : ?>
        <!-- Default sidebar content if no widgets are added -->
        <div class="widget">
            <h3 class="widget-title"><?php _e('Search Properties', 'basin-real-estate'); ?></h3>
            <?php get_search_form(); ?>
        </div>
        
        <div class="widget">
            <h3 class="widget-title"><?php _e('Property Directories', 'basin-real-estate'); ?></h3>
            <?php
            $directories = get_terms(array(
                'taxonomy' => 'directory',
                'hide_empty' => false,
            ));
            
            if (!empty($directories) && !is_wp_error($directories)) :
            ?>
                <ul>
                    <?php foreach ($directories as $directory) : ?>
                        <li>
                            <a href="<?php echo get_term_link($directory); ?>">
                                <?php echo esc_html($directory->name); ?>
                                <span class="count">(<?php echo $directory->count; ?>)</span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        
        <div class="widget">
            <h3 class="widget-title"><?php _e('Recent Listings', 'basin-real-estate'); ?></h3>
            <?php
            $recent_listings = new WP_Query(array(
                'post_type' => 'listing',
                'posts_per_page' => 5,
                'post_status' => 'publish'
            ));
            
            if ($recent_listings->have_posts()) :
            ?>
                <ul>
                    <?php while ($recent_listings->have_posts()) : $recent_listings->the_post(); ?>
                        <li>
                            <a href="<?php the_permalink(); ?>">
                                <?php the_title(); ?>
                                <?php
                                $price = get_post_meta(get_the_ID(), '_listing_price', true);
                                if ($price) :
                                ?>
                                    <br><small>$<?php echo number_format($price); ?></small>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endwhile; ?>
                </ul>
                <?php wp_reset_postdata(); ?>
            <?php endif; ?>
        </div>
        
        <div class="widget">
            <h3 class="widget-title"><?php _e('About Basin Real Estate', 'basin-real-estate'); ?></h3>
            <p><?php _e('Your trusted guide to Permian Basin real estate. We provide comprehensive listings and expert guidance for all your property needs in the region.', 'basin-real-estate'); ?></p>
        </div>
        
        <div class="widget">
            <h3 class="widget-title"><?php _e('Contact Information', 'basin-real-estate'); ?></h3>
            <div class="contact-info">
                <p><strong><?php _e('Phone:', 'basin-real-estate'); ?></strong> (*************</p>
                <p><strong><?php _e('Email:', 'basin-real-estate'); ?></strong> <EMAIL></p>
                <p><strong><?php _e('Office:', 'basin-real-estate'); ?></strong><br>
                123 Main Street<br>
                Midland, TX 79701</p>
            </div>
        </div>
    <?php endif; ?>
</aside>
