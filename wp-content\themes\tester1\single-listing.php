<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="site-branding">
                <?php if (has_custom_logo()) : ?>
                    <div class="site-logo">
                        <?php the_custom_logo(); ?>
                    </div>
                <?php endif; ?>
                
                <h1 class="site-title">
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                        <?php bloginfo('name'); ?>
                    </a>
                </h1>
                
                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="site-description"><?php echo $description; ?></p>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <nav id="site-navigation" class="main-navigation">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'fallback_cb'    => 'basin_real_estate_fallback_menu',
            ));
            ?>
        </div>
    </nav>

    <div id="content" class="site-content">
        <div class="container">
            <div class="content-area">
                <main id="main" class="site-main">

                    <?php while (have_posts()) : the_post(); ?>

                        <article id="post-<?php the_ID(); ?>" <?php post_class('listing-single'); ?>>
                            <header class="listing-header">
                                <h1 class="listing-title"><?php the_title(); ?></h1>
                                
                                <?php
                                $price = get_post_meta(get_the_ID(), '_listing_price', true);
                                $address = get_post_meta(get_the_ID(), '_listing_address', true);
                                $bedrooms = get_post_meta(get_the_ID(), '_listing_bedrooms', true);
                                $bathrooms = get_post_meta(get_the_ID(), '_listing_bathrooms', true);
                                $square_feet = get_post_meta(get_the_ID(), '_listing_square_feet', true);
                                ?>
                                
                                <div class="listing-summary">
                                    <?php if ($price) : ?>
                                        <div class="listing-price-large">$<?php echo number_format($price); ?></div>
                                    <?php endif; ?>
                                    
                                    <?php if ($address) : ?>
                                        <div class="listing-address-large"><?php echo esc_html($address); ?></div>
                                    <?php endif; ?>
                                    
                                    <div class="listing-features-large">
                                        <?php if ($bedrooms) : ?>
                                            <span class="feature-item">
                                                <strong><?php echo $bedrooms; ?></strong> <?php _e('Bedrooms', 'basin-real-estate'); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($bathrooms) : ?>
                                            <span class="feature-item">
                                                <strong><?php echo $bathrooms; ?></strong> <?php _e('Bathrooms', 'basin-real-estate'); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($square_feet) : ?>
                                            <span class="feature-item">
                                                <strong><?php echo number_format($square_feet); ?></strong> <?php _e('Sq Ft', 'basin-real-estate'); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </header>

                            <?php if (has_post_thumbnail()) : ?>
                                <div class="listing-featured-image">
                                    <?php the_post_thumbnail('large', array('class' => 'responsive-image')); ?>
                                </div>
                            <?php endif; ?>

                            <div class="listing-content">
                                <div class="listing-description">
                                    <h2><?php _e('Property Description', 'basin-real-estate'); ?></h2>
                                    <?php the_content(); ?>
                                </div>
                                
                                <div class="listing-directories">
                                    <h3><?php _e('Property Categories', 'basin-real-estate'); ?></h3>
                                    <?php
                                    $terms = get_the_terms(get_the_ID(), 'directory');
                                    if ($terms && !is_wp_error($terms)) :
                                        foreach ($terms as $term) :
                                    ?>
                                        <span class="directory-tag">
                                            <a href="<?php echo get_term_link($term); ?>"><?php echo esc_html($term->name); ?></a>
                                        </span>
                                    <?php endforeach; endif; ?>
                                </div>
                                
                                <div class="listing-contact">
                                    <h3><?php _e('Contact Information', 'basin-real-estate'); ?></h3>
                                    <div class="contact-details">
                                        <p><strong><?php _e('Interested in this property?', 'basin-real-estate'); ?></strong></p>
                                        <p><?php _e('Contact us today for more information or to schedule a viewing.', 'basin-real-estate'); ?></p>
                                        <div class="contact-buttons">
                                            <a href="tel:+14325550123" class="btn btn-primary"><?php _e('Call Now', 'basin-real-estate'); ?></a>
                                            <a href="mailto:<EMAIL>?subject=Inquiry about <?php echo urlencode(get_the_title()); ?>" class="btn"><?php _e('Email Us', 'basin-real-estate'); ?></a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <footer class="listing-footer">
                                <div class="listing-meta">
                                    <span class="posted-on">
                                        <?php _e('Listed on', 'basin-real-estate'); ?>
                                        <time datetime="<?php echo get_the_date('c'); ?>"><?php echo get_the_date(); ?></time>
                                    </span>
                                    <?php if (get_edit_post_link()) : ?>
                                        <span class="edit-link">
                                            <?php edit_post_link(__('Edit Listing', 'basin-real-estate')); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="listing-navigation">
                                    <?php
                                    $prev_post = get_previous_post();
                                    $next_post = get_next_post();
                                    ?>
                                    
                                    <?php if ($prev_post) : ?>
                                        <div class="nav-previous">
                                            <a href="<?php echo get_permalink($prev_post->ID); ?>" rel="prev">
                                                &laquo; <?php echo get_the_title($prev_post->ID); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($next_post) : ?>
                                        <div class="nav-next">
                                            <a href="<?php echo get_permalink($next_post->ID); ?>" rel="next">
                                                <?php echo get_the_title($next_post->ID); ?> &raquo;
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </footer>
                        </article>

                        <?php
                        // If comments are open or we have at least one comment, load up the comment template.
                        if (comments_open() || get_comments_number()) :
                            comments_template();
                        endif;
                        ?>

                    <?php endwhile; ?>

                </main>

                <?php get_sidebar(); ?>
            </div>
        </div>
    </div>

    <footer id="colophon" class="site-footer">
        <div class="container">
            <?php if (is_active_sidebar('footer-1')) : ?>
                <div class="footer-widgets">
                    <?php dynamic_sidebar('footer-1'); ?>
                </div>
            <?php endif; ?>
            
            <div class="site-info">
                <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('Permian Basin Real Estate Guide', 'basin-real-estate'); ?></p>
            </div>
        </div>
    </footer>
</div>

<?php wp_footer(); ?>

</body>
</html>
