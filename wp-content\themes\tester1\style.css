/*
Theme Name: Basin Real Estate
Description: A modern, responsive WordPress theme for Permian Basin Real Estate Guide. Features custom post types for listings and hierarchical directory taxonomy.
Author: Basin Real Estate Team
Version: 1.0
Text Domain: basin-real-estate
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.site-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.site-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-align: center;
}

.site-description {
    font-size: 1.1rem;
    text-align: center;
    opacity: 0.9;
}

/* Navigation */
.main-navigation {
    background-color: #34495e;
    padding: 0.5rem 0;
}

.nav-menu {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.nav-menu li {
    margin: 0 1rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.nav-menu a:hover {
    background-color: #2c3e50;
}

/* Main Content */
.site-main {
    padding: 2rem 0;
    min-height: 60vh;
}

.content-area {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
}

/* Posts and Listings */
.post, .listing {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.post:hover, .listing:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 25px rgba(0,0,0,0.15);
}

.post-header, .listing-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
}

.post-title, .listing-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.post-meta, .listing-meta {
    color: #666;
    font-size: 0.9rem;
}

.post-content, .listing-content {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-primary {
    background-color: #e74c3c;
}

.btn-primary:hover {
    background-color: #c0392b;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Footer */
.site-footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (min-width: 768px) {
    .content-area {
        grid-template-columns: 2fr 1fr;
    }

    .site-title {
        font-size: 3rem;
    }

    .nav-menu {
        justify-content: center;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 40px;
    }

    .site-main {
        padding: 3rem 0;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }

/* Real Estate Specific Styles */
.listing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.listing-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.listing-card:hover {
    transform: translateY(-3px);
}

.listing-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.listing-details {
    padding: 1.5rem;
}

.listing-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.listing-address {
    color: #666;
    margin-bottom: 1rem;
}

.directory-filter {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.directory-filter h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.filter-list {
    list-style: none;
}

.filter-list li {
    margin-bottom: 0.5rem;
}

.filter-list a {
    color: #3498db;
    text-decoration: none;
    padding: 0.25rem 0;
    display: block;
    transition: color 0.3s ease;
}

.filter-list a:hover {
    color: #2980b9;
}

/* Page Specific Styles */
.page {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.page-header {
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-align: center;
}

.page-featured-image {
    margin-top: 1.5rem;
    text-align: center;
}

.responsive-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.page-content {
    padding: 2rem;
    line-height: 1.8;
}

.page-content h2,
.page-content h3,
.page-content h4 {
    color: #2c3e50;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.page-content h2 {
    font-size: 2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.page-content h3 {
    font-size: 1.5rem;
}

.page-content h4 {
    font-size: 1.25rem;
}

.page-content p {
    margin-bottom: 1.5rem;
}

.page-content ul,
.page-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.page-content li {
    margin-bottom: 0.5rem;
}

.page-content blockquote {
    background: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 1rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    border-radius: 0 8px 8px 0;
}

.page-links {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.page-links a {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.page-links a:hover {
    background-color: #2980b9;
}

/* Listing Features Styling */
.listing-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.listing-features span {
    background-color: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.directory-tag {
    display: inline-block;
    margin: 0.25rem 0.5rem 0.25rem 0;
}

.directory-tag a {
    background-color: #e74c3c;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.85rem;
    transition: background-color 0.3s ease;
}

.directory-tag a:hover {
    background-color: #c0392b;
}

/* Sidebar Styles */
.sidebar {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1.5rem;
    margin-top: 2rem;
}

.widget {
    margin-bottom: 2rem;
}

.widget-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.widget ul {
    list-style: none;
}

.widget li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.widget li:last-child {
    border-bottom: none;
}

.widget a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.widget a:hover {
    color: #2980b9;
}

/* Comments Styles */
.comments-area {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-top: 2rem;
}

.comments-title {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.comment-list {
    list-style: none;
}

.comment {
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.comment-author {
    font-weight: 600;
    color: #2c3e50;
}

.comment-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

/* Mobile Optimizations */
@media (max-width: 767px) {
    .site-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 1.8rem;
    }

    .nav-menu {
        flex-direction: column;
        align-items: center;
    }

    .nav-menu li {
        margin: 0.25rem 0;
    }

    .listing-grid {
        grid-template-columns: 1fr;
    }

    .listing-features {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-content {
        padding: 1rem;
    }

    .page-header {
        padding: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}

/* Tablet Optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
    .listing-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .content-area {
        grid-template-columns: 2fr 1fr;
        gap: 1.5rem;
    }
}

/* Single Listing Styles */
.listing-single {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.listing-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid #dee2e6;
}

.listing-price-large {
    font-size: 3rem;
    font-weight: 700;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.listing-address-large {
    font-size: 1.5rem;
    color: #666;
    margin-bottom: 1.5rem;
}

.listing-features-large {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.feature-item {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
    min-width: 120px;
}

.feature-item strong {
    display: block;
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.listing-featured-image {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
}

.listing-content {
    padding: 2rem;
}

.listing-description h2 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.listing-contact {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin-top: 2rem;
    text-align: center;
}

.contact-buttons {
    margin-top: 1rem;
}

.contact-buttons .btn {
    margin: 0 0.5rem;
}

.listing-footer {
    background: #f8f9fa;
    padding: 1.5rem 2rem;
    border-top: 1px solid #dee2e6;
}

.listing-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.listing-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.listing-navigation a {
    color: #3498db;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.listing-navigation a:hover {
    background-color: #3498db;
    color: white;
}

/* Archive Styles */
.archive-description {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    color: #666;
}

/* Search Form Styles */
.search-form {
    display: flex;
    margin-bottom: 1rem;
}

.search-field {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
}

.search-submit {
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-submit:hover {
    background-color: #2980b9;
}

/* Contact Info Styles */
.contact-info p {
    margin-bottom: 0.5rem;
}

.contact-info strong {
    color: #2c3e50;
}

/* Print Styles */
@media print {
    .site-header,
    .main-navigation,
    .sidebar,
    .site-footer,
    .btn {
        display: none;
    }

    .site-content {
        margin: 0;
        padding: 0;
    }

    .page,
    .post,
    .listing {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .listing-price-large {
        color: #000 !important;
    }
}
