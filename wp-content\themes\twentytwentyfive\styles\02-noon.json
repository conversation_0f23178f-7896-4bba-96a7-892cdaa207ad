{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "<PERSON>on", "settings": {"color": {"palette": [{"color": "#F8F7F5", "name": "Base", "slug": "base"}, {"color": "#191919", "name": "Contrast", "slug": "contrast"}, {"color": "#FFFFFF", "name": "Accent 1", "slug": "accent-1"}, {"color": "#F5B684", "name": "Accent 2", "slug": "accent-2"}, {"color": "#191919", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#F1EEE9", "name": "Accent 5", "slug": "accent-5"}, {"color": "#19191933", "name": "Accent 6", "slug": "accent-6"}]}, "typography": {"fontFamilies": [{"name": "Beiruti", "slug": "beiruti", "fontFamily": "Beiruti, sans-serif", "fontFace": [{"fontFamily": "Beiruti", "fontStyle": "normal", "fontWeight": "200 900", "src": ["file:./assets/fonts/beiruti/Beiruti-VariableFont_wght.woff2"]}]}, {"name": "Literata", "slug": "literata", "fontFamily": "Literata, serif", "fontFace": [{"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLight.woff2"], "fontWeight": "200", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLightItalic.woff2"], "fontWeight": "200", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Light.woff2"], "fontWeight": "300", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-LightItalic.woff2"], "fontWeight": "300", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Regular.woff2"], "fontWeight": "400", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-RegularItalic.woff2"], "fontWeight": "400", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Medium.woff2"], "fontWeight": "500", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-MediumItalic.woff2"], "fontWeight": "500", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBold.woff2"], "fontWeight": "600", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBoldItalic.woff2"], "fontWeight": "600", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Bold.woff2"], "fontWeight": "700", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BoldItalic.woff2"], "fontWeight": "700", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBold.woff2"], "fontWeight": "800", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBoldItalic.woff2"], "fontWeight": "800", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Black.woff2"], "fontWeight": "900", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BlackItalic.woff2"], "fontWeight": "900", "fontStyle": "italic", "fontFamily": "Literata"}]}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.9rem", "slug": "small"}, {"fluid": {"max": "1.2rem", "min": "1rem"}, "name": "Medium", "size": "1rem", "slug": "medium"}, {"fluid": {"max": "1.8rem", "min": "1.6rem"}, "name": "Large", "size": "1.6rem", "slug": "large"}, {"fluid": {"max": "2.2rem", "min": "1.8em"}, "name": "Extra Large", "size": "1.8rem", "slug": "x-large"}, {"fluid": {"max": "2.8rem", "min": "2rem"}, "name": "Extra Extra Large", "size": "2rem", "slug": "xx-large"}]}}, "styles": {"color": {"text": "var:preset|color|accent-4"}, "typography": {"fontFamily": "var:preset|font-family|literata", "fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.01em", "lineHeight": "1.6"}, "blocks": {"core/button": {"border": {"color": "var:preset|color|contrast"}, "shadow": "var:preset|shadow|natural", "spacing": {"padding": {"bottom": "0.6rem", "left": "1.6rem", "right": "1.6rem", "top": "0.6rem"}}, "typography": {"fontFamily": "var:preset|font-family|beiruti"}, "variations": {"outline": {"shadow": "none", "spacing": {"padding": {"bottom": "0.6rem", "left": "1.6rem", "right": "1.6rem", "top": "0.6rem"}}}}}, "core/list": {"typography": {"lineHeight": "1.3"}}, "core/loginout": {"typography": {"fontSize": "var:preset|font-size|medium"}}, "core/post-terms": {"typography": {"fontWeight": "300"}}, "core/post-title": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}, "typography": {"fontFamily": "var:preset|font-family|beiruti", "fontWeight": "500", "lineHeight": "1"}}, "core/quote": {"typography": {"fontSize": "var:preset|font-size|medium"}}, "core/query-pagination": {"typography": {"fontWeight": "300"}}, "core/query-title": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/site-tagline": {"typography": {"fontSize": "var:preset|font-size|small"}}, "core/site-title": {"typography": {"fontFamily": "var:preset|font-family|beiruti", "fontWeight": "600", "letterSpacing": "2.4px", "textTransform": "uppercase"}}}, "elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}}, "h4": {"typography": {"fontSize": "var:preset|font-size|large"}}, "h5": {"typography": {"fontSize": "var:preset|font-size|medium", "letterSpacing": "0px"}}, "h6": {"typography": {"fontSize": "var:preset|font-size|small"}}, "heading": {"color": {"text": "var:preset|color|accent-3"}, "typography": {"fontFamily": "var:preset|font-family|beiruti", "fontWeight": "500", "letterSpacing": "-0.02em", "lineHeight": "1.02"}}, "link": {"color": {"text": "currentColor"}}}, "variations": {"section-4": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"button": {":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}}, "heading": {"color": {"text": "currentColor"}}, "link": {"color": {"text": "currentColor"}}}}, "section-5": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}, "heading": {"color": {"text": "var:preset|color|base"}}, "link": {"color": {"text": "currentColor"}}}}}}}