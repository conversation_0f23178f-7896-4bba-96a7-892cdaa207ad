{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Dusk", "settings": {"color": {"palette": [{"color": "#E2E2E2", "name": "Base", "slug": "base"}, {"color": "#3B3B3B", "name": "Contrast", "slug": "contrast"}, {"color": "#F5EDFF", "name": "Accent 1", "slug": "accent-1"}, {"color": "#650DD4", "name": "Accent 2", "slug": "accent-2"}, {"color": "#191919", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#DBDBDB", "name": "Accent 5", "slug": "accent-5"}, {"color": "#3B3B3B33", "name": "Accent 6", "slug": "accent-6"}]}, "custom": {"color": {"accent-2-opacity-20": "#650DD433"}}}, "styles": {"blocks": {"core/code": {"color": {"text": "var:preset|color|black", "background": "var:preset|color|accent-5"}}, "core/paragraph": {"elements": {"link": {"color": {"text": "var:preset|color|accent-2"}}}}, "core/post-author-name": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/post-terms": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"link": {"color": {"text": "currentColor"}}}}, "core/post-title": {"elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|black"}, "elements": {"cite": {"color": {"text": "var:preset|color|contrast"}}}}, "core/quote": {"color": {"text": "var:preset|color|black"}}, "core/site-title": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}}, "elements": {"button": {"color": {"text": "var:preset|color|base", "background": "var:preset|color|accent-2"}, "border": {"color": "transparent"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}}, "heading": {"color": {"text": "var:preset|color|accent-3"}}, "link": {"color": {"text": "var:preset|color|accent-3"}}}, "variations": {"post-terms-1": {"elements": {"link": {"border": {"color": "var:custom|color|accent-2-opacity-20"}}}}, "section-1": {"elements": {"link": {"color": {"text": "var:preset|color|accent-3"}}}}, "section-2": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {"color": {"background": "var:preset|color|accent-3", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-3) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}, "heading": {"color": {"text": "currentColor"}}}}, "section-3": {"color": {"text": "var:preset|color|accent-2"}, "elements": {"heading": {"color": {"text": "currentColor"}}}}, "section-4": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {"color": {"text": "var:preset|color|base"}, ":hover": {"color": {"text": "var:preset|color|base"}}}, "link": {"color": {"text": "currentColor"}}, "heading": {"color": {"text": "currentColor"}}}}, "section-5": {"elements": {"button": {"color": {"background": "var:preset|color|accent-2", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)", "text": "var:preset|color|base"}}}, "heading": {"color": {"text": "var:preset|color|base"}}, "link": {"color": {"text": "var:preset|color|base"}}}}}}}