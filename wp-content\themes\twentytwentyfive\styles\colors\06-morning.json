{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Morning", "settings": {"color": {"palette": [{"color": "#DFDCD7", "name": "Base", "slug": "base"}, {"color": "#191919", "name": "Contrast", "slug": "contrast"}, {"color": "#7A9BDB", "name": "Accent 1", "slug": "accent-1"}, {"color": "#F7E6FF", "name": "Accent 2", "slug": "accent-2"}, {"color": "#182949", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#D7D3CC", "name": "Accent 5", "slug": "accent-5"}, {"color": "#19191933", "name": "Accent 6", "slug": "accent-6"}]}}, "styles": {"color": {"text": "var:preset|color|accent-4"}, "blocks": {"core/code": {"color": {"text": "var:preset|color|contrast", "background": "var:preset|color|accent-5"}}, "core/paragraph": {"elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-author-name": {"elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-title": {"elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|contrast"}, "elements": {"cite": {"color": {"text": "var:preset|color|accent-4"}}}}, "core/quote": {"elements": {"cite": {"color": {"text": "var:preset|color|accent-4"}}}}}, "elements": {"button": {"color": {"text": "var:preset|color|contrast", "background": "var:preset|color|accent-1"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)", "text": "var:preset|color|contrast"}}}, "heading": {"color": {"text": "var:preset|color|contrast"}}, "link": {"color": {"text": "currentColor"}}}, "variations": {"post-terms-1": {"elements": {"link": {"color": {"background": "var:preset|color|accent-5"}, "border": {"color": "var:preset|color|accent-5"}}}}, "section-2": {"elements": {"button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)"}}}}}, "section-3": {"elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--contrast) 85%, transparent)"}}}}}, "section-4": {"color": {"text": "var:preset|color|base"}, "elements": {"heading": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)", "text": "var:preset|color|contrast"}}}, "link": {"color": {"text": "currentColor"}}}}, "section-5": {"elements": {"heading": {"color": {"text": "var:preset|color|base"}}, "button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}}}}}}