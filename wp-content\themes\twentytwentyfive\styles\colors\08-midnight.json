{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Midnight", "settings": {"color": {"duotone": [{"colors": ["#4433A6", "#79F3B1"], "name": "Midnight filter", "slug": "midnight-filter"}], "palette": [{"color": "#4433A6", "name": "Base", "slug": "base"}, {"color": "#79F3B1", "name": "Contrast", "slug": "contrast"}, {"color": "#5644BC", "name": "Accent 1", "slug": "accent-1"}, {"color": "#372696", "name": "Accent 2", "slug": "accent-2"}, {"color": "#251D51", "name": "Accent 3", "slug": "accent-3"}, {"color": "#79F3B1", "name": "Accent 4", "slug": "accent-4"}, {"color": "#E8B7FF", "name": "Accent 5", "slug": "accent-5"}, {"color": "#79F3B133", "name": "Accent 6", "slug": "accent-6"}]}}, "styles": {"blocks": {"core/code": {"color": {"background": "var:preset|color|accent-2", "text": "var:preset|color|contrast"}}, "core/post-date": {"color": {"text": "var:preset|color|contrast"}, "elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}}, "elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--contrast) 85%, transparent)"}}}}, "variations": {"section-1": {"color": {"text": "var:preset|color|accent-3"}, "elements": {"button": {"color": {"background": "var:preset|color|accent-3", "text": "var:preset|color|accent-5"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-3) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}}}, "section-4": {"color": {"text": "var:preset|color|accent-5"}, "elements": {"button": {"color": {"background": "var:preset|color|accent-5", "text": "var:preset|color|accent-3"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-5) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}}}}}}