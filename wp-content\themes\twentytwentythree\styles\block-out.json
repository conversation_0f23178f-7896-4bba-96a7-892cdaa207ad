{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "title": "Block out", "settings": {"color": {"duotone": [{"colors": ["#E2161D", "#FF9C9C"], "slug": "default-filter", "name": "Default filter"}], "palette": [{"color": "#ff5252", "name": "Base", "slug": "base"}, {"color": "#252525", "name": "Contrast", "slug": "contrast"}, {"color": "#ffffff", "name": "Primary", "slug": "primary"}, {"color": "#ff2d34", "name": "Secondary", "slug": "secondary"}, {"color": "#ff7e7e", "name": "Tertiary", "slug": "tertiary"}]}, "layout": {"contentSize": "800px"}, "typography": {"fontSizes": [{"fluid": {"max": "1rem", "min": "0.875rem"}, "size": "1rem", "slug": "small"}, {"fluid": {"max": "1.125rem", "min": "1rem"}, "size": "1.125rem", "slug": "medium"}, {"fluid": false, "size": "1.75rem", "slug": "large"}, {"fluid": false, "size": "2.25rem", "slug": "x-large"}, {"fluid": {"max": "7rem", "min": "4.3rem"}, "size": "7rem", "slug": "xx-large"}]}}, "styles": {"blocks": {"core/avatar": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/image": {"border": {"radius": "8px"}, "filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/navigation": {"typography": {"fontSize": "var(--wp--preset--font-size--large)"}}, "core/post-content": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--contrast)"}}, "h1": {"color": {"text": "var(--wp--preset--color--contrast)"}}}}, "core/post-featured-image": {"border": {"radius": "8px"}, "filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/post-title": {"elements": {"link": {":active": {"color": {"text": "var(--wp--preset--color--primary)"}}}}, "color": {"text": "var(--wp--preset--color--primary)"}}, "core/quote": {"border": {"width": "1px"}}, "core/search": {"border": {"radius": "8px"}}, "core/site-logo": {"filter": {"duotone": "var(--wp--preset--duotone--default-filter)"}}, "core/site-title": {"elements": {"link": {":active": {"color": {"text": "var(--wp--preset--color--primary)"}}}}, "spacing": {"padding": {"bottom": "var(--wp--preset--spacing--30)", "top": "var(--wp--preset--spacing--30)"}}, "typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "lineHeight": "1.1", "textTransform": "lowercase"}}, "core/query": {"elements": {"h2": {"typography": {"fontSize": "var(--wp--preset--font-size--large)"}}}}}, "elements": {"button": {"border": {"radius": "8px"}, "typography": {"fontFamily": "var(--wp--preset--font-family--ibm-plex-mono)", "fontStyle": "italic", "fontWeight": "400"}, ":active": {"color": {"text": "var(--wp--preset--color--contrast)"}}}, "h1": {"color": {"text": "var(--wp--preset--color--primary)"}}, "h6": {"typography": {"fontWeight": "400"}}, "heading": {"typography": {"fontFamily": "var(--wp--preset--font-family--ibm-plex-mono)", "fontStyle": "italic"}}, "link": {":active": {"color": {"text": "var(--wp--preset--color--primary)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--ibm-plex-mono)", "fontStyle": "italic", "fontWeight": "400"}}}, "spacing": {"padding": {"bottom": "0px", "top": "0px"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--dm-sans)"}}}